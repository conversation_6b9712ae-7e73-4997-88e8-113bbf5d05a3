plugins {
    id("com.android.library")
}

android {
    namespace = "org.opencv"
    compileSdk = 36

    defaultConfig {
        minSdk = 24
        targetSdk = 36
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    sourceSets {
        getByName("main") {
            jniLibs.srcDirs("Z:/cheese/OpenCV/OpenCV-5.0-android-sdk/sdk/native/libs")
            java.srcDirs("Z:/cheese/OpenCV/OpenCV-5.0-android-sdk/sdk/java/src")
            aidl.srcDirs("Z:/cheese/OpenCV/OpenCV-5.0-android-sdk/sdk/java/src")
            res.srcDirs("Z:/cheese/OpenCV/OpenCV-5.0-android-sdk/sdk/java/res")
        }
    }
}
