package com.example.opencva

import android.Manifest
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.opencva.ui.theme.OPENCVATheme
import com.example.opencva.ui.*
import kotlinx.coroutines.launch

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            OPENCVATheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    TemplateMatchingApp()
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TemplateMatchingApp(viewModel: TemplateMatchingViewModel = viewModel()) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    // 权限请求
    val permissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (!isGranted) {
            Toast.makeText(context, "需要存储权限来选择图片", Toast.LENGTH_SHORT).show()
        }
    }

    // 图片选择器
    val sourceImageLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let { viewModel.setSourceImage(context, it) }
    }

    val templateImageLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let { viewModel.setTemplateImage(context, it) }
    }

    // 检查权限
    LaunchedEffect(Unit) {
        if (ContextCompat.checkSelfPermission(
                context, Manifest.permission.READ_EXTERNAL_STORAGE
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            permissionLauncher.launch(Manifest.permission.READ_EXTERNAL_STORAGE)
        }

        // 初始化匹配器
        viewModel.initializeMatcher()
    }

    val uiState by viewModel.uiState.collectAsState()

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        "OpenCV 模板匹配器",
                        fontWeight = FontWeight.Bold
                    )
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 图片选择区域
            item {
                ImageSelectionSection(
                    sourceImage = uiState.sourceImage,
                    templateImage = uiState.templateImage,
                    onSelectSourceImage = { sourceImageLauncher.launch("image/*") },
                    onSelectTemplateImage = { templateImageLauncher.launch("image/*") }
                )
            }

            // 参数配置区域
            item {
                ParameterConfigSection(
                    config = uiState.matchConfig,
                    onConfigChange = viewModel::updateConfig,
                    presetMode = uiState.presetMode,
                    onPresetChange = viewModel::setPresetMode
                )
            }

            // 控制按钮
            item {
                ControlButtonsSection(
                    isMatching = uiState.isMatching,
                    canMatch = uiState.sourceImage != null && uiState.templateImage != null,
                    onStartMatch = {
                        scope.launch {
                            viewModel.performMatching()
                        }
                    },
                    onBenchmark = {
                        scope.launch {
                            viewModel.runBenchmark()
                        }
                    }
                )
            }

            // 结果显示区域
            uiState.matchResult?.let { result ->
                item {
                    ResultDisplaySection(result = result)
                }
            }

            // 性能统计
            uiState.performanceStats?.let { stats ->
                item {
                    PerformanceStatsSection(stats = stats)
                }
            }

            // 基准测试结果
            uiState.benchmarkResult?.let { benchmark ->
                item {
                    BenchmarkResultSection(benchmark = benchmark)
                }
            }
        }
    }
}